import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    Components({
      resolvers: [VantResolver()],
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  server: {
    port: 3000,
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://h5.zgjcks.com',
        changeOrigin: true,
        secure: false,
      },
      '/wechat-api': {
        target: 'http://zg99.offcn.com',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/wechat-api/, ''),
      },
    },
  },
}) 
