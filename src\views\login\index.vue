<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from '@/utils/toast'
import { login } from '@/api/user'
import { useUserStore } from '@/stores/user'

// 路由实例
const router = useRouter()

// 用户状态
const userStore = useUserStore()

// 表单数据
const loginForm = reactive({
  phone: '',
  password: ''
})

// 加载状态
const loading = ref(false)

// 登录方法
const handleLogin = async () => {
  // 表单验证
  if (!loginForm.phone || !loginForm.password) {
    showToast('手机号和密码不能为空')
    return
  }
  
  // 手机号格式验证
  const phoneReg = /^1[3-9]\d{9}$/
  if (!phoneReg.test(loginForm.phone)) {
    showToast('请输入正确的手机号码')
    return
  }
  
  // 开始登录
  loading.value = true
  try {
    // 调用真实登录API
    const res = await login({
      phone: loginForm.phone,
      pwd: loginForm.password
    })
    
    console.log('登录响应:', res)
    console.log('响应类型:', typeof res)
    console.log('响应内容:', JSON.stringify(res, null, 2))
    
    // 根据项目API规范处理登录响应
    // API响应格式: {code: number, msg: string, data: any, time: string}
    // code为1表示成功，code为0表示失败
    if (res && res.code === 1) {
      // 登录成功，处理响应数据
      const responseData = res.data || {}
      
      // 提取用户信息
      const userInfo = responseData
      if (!userInfo || !userInfo.id) {
        showToast('登录失败：未获取到用户信息')
        return
      }

      // 保存登录信息
      userStore.setUserInfo(userInfo)

      // 提示成功并跳转
      showToast('登录成功')

      // 获取登录前想要访问的目标URL
      const redirectUrl = userStore.getAndClearRedirectUrl()

      // 如果有保存的目标URL，则跳转到目标页面，否则跳转到默认页面
      if (redirectUrl && redirectUrl !== '/login') {
        router.push(redirectUrl)
      } else {
        router.push('/tasks')
      }
      
    } else {
      // 登录失败，错误信息已在拦截器中显示，这里不需要重复显示
      console.log('登录失败，错误信息已在拦截器中显示:', res?.msg)
    }

    
  } catch (error: any) {
    console.error('登录失败:', error)
    // 显示错误信息
    const errorMsg = error?.message || error?.response?.msg || '登录失败，请重试'
    showToast(errorMsg)
  } finally {
    loading.value = false
  }
}

// 跳转到注册页面
const toRegister = () => {
  router.push('/register')
}

// 跳转到找回密码页面
const toForgotPassword = () => {
  router.push('/forgot-password')
}

</script>

<template>
  <div class="login-container">
    <!-- 简化的头部 -->
    <div class="login-header">
      <h1 class="page-title">登录</h1>
    </div>
    
    <!-- 登录表单 -->
    <div class="login-form">
      <div class="form-card">
        <van-form @submit="handleLogin">
          <div class="form-fields">
            <van-field
              v-model="loginForm.phone"
              name="phone"
              placeholder="请输入手机号"
              type="tel"
              maxlength="11"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请输入手机号' }]"
            >
              <template #left-icon>
                <van-icon name="phone-o" />
              </template>
            </van-field>
            
            <van-field
              v-model="loginForm.password"
              type="password"
              name="password"
              placeholder="请输入密码"
              clearable
              :border="false"
              :rules="[{ required: true, message: '请输入密码' }]"
            >
              <template #left-icon>
                <van-icon name="lock" />
              </template>
            </van-field>
          </div>
          
          <div class="form-actions">
            <van-button 
              round 
              block 
              type="primary" 
              native-type="submit" 
              :loading="loading"
              size="large"
              color="#ff6b35"
              class="login-btn"
            >
              登录
            </van-button>
          </div>
          
          <div class="form-footer">
            <div class="register-link" @click="toRegister">
              注册账号
            </div>
            <div class="forgot-password" @click="toForgotPassword">
              <span class="link-text">忘记密码</span>
            </div>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #ff6741 0%, #ff8c5c 100%);
  display: flex;
  flex-direction: column;
}

.login-header {
  padding: 60px 20px 40px;
  text-align: center;
}

.page-title {
  font-size: 32px;
  font-weight: 600;
  color: #fff;
  margin: 0;
  letter-spacing: 1px;
}

.login-form {
  flex: 1;
  padding: 0 20px;
}

.form-card {
  background: #fff;
  border-radius: 20px;
  padding: 40px 30px;
  box-shadow: 0 -8px 30px rgba(0, 0, 0, 0.1);
}

.form-fields {
  margin-bottom: 40px;
}

.form-fields .van-field {
  background: #f5f5f5;
  border-radius: 12px;
  margin-bottom: 20px;
  padding: 0 20px;
  height: 56px;
  display: flex;
  align-items: center;
}

.form-actions {
  margin-bottom: 30px;
}

.login-btn {
  margin-bottom: 15px;
  height: 50px !important;
  font-size: 18px;
  font-weight: 600;
}

.quick-login-btn {
  height: 50px !important;
  background: #f8f9fa !important;
  border: 1px solid #e8e8e8 !important;
  color: #646566 !important;
  font-size: 16px;
}

.form-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.register-link {
  font-size: 15px;
  color: #ff6741;
  font-weight: 500;
  cursor: pointer;
}

.forgot-password {
  font-size: 15px;
  cursor: pointer;
}

.link-text {
  color: #ff6741;
  font-weight: 500;
}

/* 自定义字段样式 */
:deep(.van-field__left-icon) {
  color: #969799;
  margin-right: 12px;
  font-size: 18px;
}

:deep(.van-field__control) {
  font-size: 16px;
  color: #333;
  line-height: 1.5;
}

:deep(.van-field__control)::placeholder {
  color: #969799;
  font-size: 16px;
  line-height: 1.5;
}

:deep(.van-field) {
  border: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  min-height: 56px;
}

:deep(.van-field__body) {
  display: flex;
  align-items: center;
  width: 100%;
}

:deep(.van-field--focused) {
  background: #fff;
  box-shadow: 0 0 0 2px rgba(255, 103, 65, 0.2);
}

:deep(.van-field__left-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.van-field__control) {
  display: flex;
  align-items: center;
}

/* 自定义按钮样式 */
:deep(.van-button--primary) {
  background: linear-gradient(90deg, #ff6741, #ff8c5c);
  border: none;
  box-shadow: 0 4px 15px rgba(255, 103, 65, 0.3);
}

:deep(.van-button--primary:active) {
  background: linear-gradient(90deg, #e55a37, #e5804f);
}
</style> 