import request from './index'

// 用户登录接口类型定义
export interface LoginParams {
  phone: string
  pwd: string
}

export interface LoginResponse {
  userInfo?: {
    id: string
    userId: string
    phone: string
    avatar: string
    nickname: string
    username: string
    level: string
    balance: number
  }
  [key: string]: any
}

// 注册接口参数类型
export interface RegisterParams {
  phone: string
  pwd: string
  repwd: string
  yzm: string
  username: string  // 新增用户工号
  nickname: string  // 新增用户姓名
  departid: number  // 新增用户部门ID
}

// 发送验证码参数类型
export interface SendCodeParams {
  phone: string
}

// 找回密码参数类型
export interface ResetPasswordParams {
  phone: string
  pwd: string
  repwd: string
  yzmcode: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
  time: string
}

// 退出登录响应类型
export interface LogoutResponse {
  code: number
  msg: string
  data: null
  time: string
}

// 用户信息详细类型定义（基于新的API返回格式）
export interface UserMemberInfo {
  id: number
  username: string
  nickname: string
  password: string
  openid: any
  salt: string
  avatar: string
  email: string
  mobile: string
  loginfailure: number
  logintime: number
  loginip: string
  lastloginip: string
  lastlogin: number
  createtime: number
  updatetime: number

  status: string
  data_scope: any
  jifen: number  // 积分
  bdsum: number  // 绑定数量
  isbdwx: number // 是否绑定微信
  shenhe: number // 审核状态
  shenhetext: string // 审核文本
}

// 获取用户信息响应类型
export interface GetMemberResponse {
  code: number
  msg: string
  time: string
  data: UserMemberInfo | null
}

// 添加媒体账户参数类型
export interface AddMediaUserParams {
  meidaid: number  // 媒体平台ID
  ncname: string   // 账户昵称
}

// 添加媒体账户响应类型
export interface AddMediaUserResponse {
  code: number
  msg: string
  time: string
  data: any
}

// 媒体账户信息类型
export interface MediaUserAccount {
  id: number
  user_id: number
  meidaid: number  // 媒体平台ID (修正字段名)
  ncname: string
  createtime: number
  updatetime: number
  status: string
  media_name?: string // 媒体平台名称（可能需要关联查询）
}

// 获取媒体账户列表响应类型
export interface GetMediaUserListResponse {
  code: number
  msg: string
  time: string
  data: MediaUserAccount[]
}

// 删除媒体账户参数类型
export interface DeleteMediaUserParams {
  mediauid: number  // 媒体账户ID
}

// 删除媒体账户响应类型
export interface DeleteMediaUserResponse {
  code: number
  msg: string
  time: string
  data: any
}

// 微信绑定参数类型
export interface BindWechatParams {
  code?: string  // 微信授权码
  openid?: string  // 微信openid
}

// 微信绑定响应类型
export interface BindWechatResponse {
  code: number
  msg: string
  time: string
  data: any
}

// 用户登录
export function login(data: LoginParams): Promise<LoginResponse> {
  return request({
    url: '/user/login',
    method: 'get',
    params: {
      phone: data.phone,
      pwd: data.pwd
    }
  })
}

// 发送验证码
export function sendCode(data: SendCodeParams): Promise<ApiResponse> {
  return request({
    url: '/user/sendsms',
    method: 'get',
    params: {
      phone: data.phone
    }
  })
}

// 用户注册
export function register(data: RegisterParams): Promise<ApiResponse> {
  return request({
    url: '/user/register',
    method: 'get',
    params: {
      phone: data.phone,
      pwd: data.pwd,
      repwd: data.repwd,
      yzm: data.yzm,
      username: data.username,  // 添加用户工号参数
      nickname: data.nickname,  // 添加用户姓名参数
      departid: data.departid   // 添加用户部门ID参数
    }
  })
}

// 找回密码
export function resetPassword(data: ResetPasswordParams): Promise<ApiResponse> {
  return request({
    url: '/user/changepwd',
    method: 'get',
    params: {
      phone: data.phone,
      pwd: data.pwd,
      repwd: data.repwd,
      yzmcode: data.yzmcode
    }
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/user/info',
    method: 'get'
  })
}

// 获取用户详细信息（新的API接口）
export function getMemberInfo(): Promise<GetMemberResponse> {
  return request({
    url: '/user/getmember',
    method: 'get'
  })
}

// 退出登录
export function logout(): Promise<LogoutResponse> {
  return request({
    url: '/user/loginout',
    method: 'get'
  })
}

// 添加媒体账户
export function addMediaUser(data: AddMediaUserParams): Promise<AddMediaUserResponse> {
  return request({
    url: '/user/addmediauser',
    method: 'get',
    params: {
      meidaid: data.meidaid,
      ncname: data.ncname
    }
  })
}

// 获取媒体账户列表
export function getMediaUserList(): Promise<GetMediaUserListResponse> {
  return request({
    url: '/user/getmedialist',
    method: 'get'
  })
}

// 删除媒体账户
export function deleteMediaUser(data: DeleteMediaUserParams): Promise<DeleteMediaUserResponse> {
  return request({
    url: '/user/delmediauser',
    method: 'get',
    params: {
      mediauid: data.mediauid
    }
  })
}

// 绑定微信
export function bindWechat(data: BindWechatParams): Promise<BindWechatResponse> {
  return request({
    url: '/user/memberbdwx',
    method: 'get',
    params: data
  })
}
